/**
 * Modern SAAS Interactions and Animations
 * Enhances user experience with smooth animations and micro-interactions
 */

$(document).ready(function() {
    
    // Initialize all modern interactions
    initScrollAnimations();
    initHoverEffects();
    initFormEnhancements();
    initLoadingStates();
    initMicroInteractions();
    initParallaxEffects();
    
    /**
     * Scroll-triggered animations using Intersection Observer
     */
    function initScrollAnimations() {
        // Create intersection observer for fade-in animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in-up');
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);
        
        // Observe elements for animation
        document.querySelectorAll('.card-modern, .section-header, .hero-content > *').forEach(el => {
            observer.observe(el);
        });
        
        // Staggered animations for grid items
        document.querySelectorAll('.grid-modern > *').forEach((el, index) => {
            el.style.animationDelay = `${index * 0.1}s`;
        });
    }
    
    /**
     * Enhanced hover effects and micro-interactions
     */
    function initHoverEffects() {
        // Card hover effects
        $('.card-modern').hover(
            function() {
                $(this).addClass('shadow-xl').css('transform', 'translateY(-8px)');
            },
            function() {
                $(this).removeClass('shadow-xl').css('transform', 'translateY(0)');
            }
        );
        
        // Button hover effects with ripple
        $('.btn-modern').on('mousedown', function(e) {
            const button = $(this);
            const ripple = $('<span class="ripple"></span>');
            
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.css({
                width: size,
                height: size,
                left: x,
                top: y
            });
            
            button.append(ripple);
            
            setTimeout(() => ripple.remove(), 600);
        });
        
        // Navigation link hover effects
        $('.nav-link-modern').hover(
            function() {
                $(this).css('transform', 'translateX(4px)');
            },
            function() {
                $(this).css('transform', 'translateX(0)');
            }
        );
    }
    
    /**
     * Form enhancements with validation feedback
     */
    function initFormEnhancements() {
        // Real-time validation
        $('.form-input-modern').on('blur', function() {
            const input = $(this);
            const value = input.val().trim();
            const type = input.attr('type');
            
            // Remove previous states
            input.removeClass('form-input-success form-input-error');
            input.siblings('.form-error-message, .form-success-message').remove();
            
            if (value === '') return;
            
            let isValid = true;
            let message = '';
            
            // Validation logic
            if (type === 'email') {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                isValid = emailRegex.test(value);
                message = isValid ? 'Valid email address' : 'Please enter a valid email address';
            } else if (input.attr('required')) {
                isValid = value.length >= 2;
                message = isValid ? 'Looks good!' : 'This field is required';
            }
            
            // Apply validation state
            if (isValid) {
                input.addClass('form-input-success');
                input.after(`<div class="form-success-message"><i class="fas fa-check-circle"></i> ${message}</div>`);
            } else {
                input.addClass('form-input-error');
                input.after(`<div class="form-error-message"><i class="fas fa-exclamation-circle"></i> ${message}</div>`);
            }
        });
        
        // Enhanced form submission
        $('#contactForm').on('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = $('#submitButton');
            const originalText = submitBtn.html();
            
            // Show loading state
            submitBtn.addClass('btn-loading').prop('disabled', true);
            
            // Simulate form submission (replace with actual AJAX call)
            setTimeout(() => {
                submitBtn.removeClass('btn-loading').prop('disabled', false);
                submitBtn.html('<i class="fas fa-check mr-2"></i>Message Sent!');
                submitBtn.addClass('btn-modern-success');
                
                // Show success notification
                showNotification('Message sent successfully!', 'success');
                
                // Reset form after delay
                setTimeout(() => {
                    this.reset();
                    submitBtn.html(originalText).removeClass('btn-modern-success');
                    $('.form-input-modern').removeClass('form-input-success form-input-error');
                    $('.form-error-message, .form-success-message').remove();
                }, 2000);
            }, 2000);
        });
    }
    
    /**
     * Loading states and skeleton screens
     */
    function initLoadingStates() {
        // Add loading skeletons for images
        $('img').each(function() {
            const img = $(this);
            const skeleton = $('<div class="skeleton"></div>');
            
            skeleton.css({
                width: img.width() || '100%',
                height: img.height() || '200px',
                position: 'absolute',
                top: 0,
                left: 0
            });
            
            img.parent().css('position', 'relative').append(skeleton);
            
            img.on('load', function() {
                skeleton.fadeOut(300, function() {
                    skeleton.remove();
                });
            });
        });
    }
    
    /**
     * Micro-interactions and delightful details
     */
    function initMicroInteractions() {
        // Floating action button effect
        $('.btn-modern-primary').on('mouseenter', function() {
            $(this).css('transform', 'scale(1.05) translateY(-2px)');
        }).on('mouseleave', function() {
            $(this).css('transform', 'scale(1) translateY(0)');
        });
        
        // Icon rotation on hover
        $('.fas, .far, .fab').parent().hover(
            function() {
                $(this).find('i').css('transform', 'rotate(5deg) scale(1.1)');
            },
            function() {
                $(this).find('i').css('transform', 'rotate(0deg) scale(1)');
            }
        );
        
        // Badge pulse animation
        $('.badge-modern').each(function() {
            $(this).css('animation', 'pulse 2s infinite');
        });
        
        // Smooth scroll for anchor links
        $('a[href^="#"]').on('click', function(e) {
            e.preventDefault();
            const target = $($(this).attr('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 80
                }, 800, 'easeInOutCubic');
            }
        });
    }
    
    /**
     * Parallax effects for hero section
     */
    function initParallaxEffects() {
        $(window).on('scroll', function() {
            const scrolled = $(this).scrollTop();
            const parallax = $('.hero-modern');
            const speed = 0.5;
            
            parallax.css('transform', `translateY(${scrolled * speed}px)`);
            
            // Fade out hero content on scroll
            const heroContent = $('.hero-content');
            const opacity = Math.max(0, 1 - scrolled / 500);
            heroContent.css('opacity', opacity);
        });
    }
    
    /**
     * Notification system
     */
    function showNotification(message, type = 'info') {
        const notification = $(`
            <div class="notification notification-${type}">
                <div class="notification-content">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
                <button class="notification-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `);
        
        $('body').append(notification);
        
        // Animate in
        setTimeout(() => notification.addClass('show'), 100);
        
        // Auto remove
        setTimeout(() => {
            notification.removeClass('show');
            setTimeout(() => notification.remove(), 300);
        }, 5000);
        
        // Manual close
        notification.find('.notification-close').on('click', function() {
            notification.removeClass('show');
            setTimeout(() => notification.remove(), 300);
        });
    }
    
    /**
     * Enhanced easing function
     */
    $.easing.easeInOutCubic = function(x, t, b, c, d) {
        if ((t /= d / 2) < 1) return c / 2 * t * t * t + b;
        return c / 2 * ((t -= 2) * t * t + 2) + b;
    };
});

// CSS for additional animations and effects
const additionalStyles = `
<style>
/* Ripple Effect */
.btn-modern {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 9999;
    max-width: 400px;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    border-left: 4px solid var(--success-500);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.notification-close {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background 0.2s;
}

.notification-close:hover {
    background: var(--secondary-100);
}

/* Enhanced transitions */
* {
    transition: transform 0.2s ease, box-shadow 0.2s ease, opacity 0.2s ease;
}

/* Smooth icon transitions */
i {
    transition: transform 0.2s ease;
}
</style>
`;

// Inject additional styles
$('head').append(additionalStyles);
