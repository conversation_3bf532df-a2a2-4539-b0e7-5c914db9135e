/**
 * Performance Optimizations for Modern SAAS Tourism Website
 * Implements lazy loading, critical CSS, and other performance enhancements
 */

$(document).ready(function() {
    
    // Initialize performance optimizations
    initLazyLoading();
    initCriticalCSS();
    initImageOptimization();
    initPreloading();
    initServiceWorker();
    initAnalytics();
    
    /**
     * Lazy Loading for Images and Content
     */
    function initLazyLoading() {
        // Intersection Observer for lazy loading
        if ('IntersectionObserver' in window) {
            const lazyImageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        
                        // Add loading animation
                        img.style.filter = 'blur(5px)';
                        img.style.transition = 'filter 0.3s';
                        
                        // Load the image
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                        }
                        
                        if (img.dataset.srcset) {
                            img.srcset = img.dataset.srcset;
                            img.removeAttribute('data-srcset');
                        }
                        
                        img.onload = () => {
                            img.style.filter = 'blur(0)';
                            img.classList.add('loaded');
                        };
                        
                        observer.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });
            
            // Observe all images with data-src
            document.querySelectorAll('img[data-src]').forEach(img => {
                lazyImageObserver.observe(img);
            });
            
            // Lazy load sections
            const sectionObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('section-loaded');
                    }
                });
            }, { threshold: 0.1 });
            
            document.querySelectorAll('.section-modern').forEach(section => {
                sectionObserver.observe(section);
            });
        }
    }
    
    /**
     * Critical CSS Loading
     */
    function initCriticalCSS() {
        // Load non-critical CSS asynchronously
        const nonCriticalCSS = [
            'plugins/fontawesome-free/css/all.min.css',
            'plugins/datatables-bs4/css/dataTables.bootstrap4.min.css',
            'plugins/select2/css/select2.min.css'
        ];
        
        nonCriticalCSS.forEach(href => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = _base_url_ + href;
            link.media = 'print';
            link.onload = function() {
                this.media = 'all';
            };
            document.head.appendChild(link);
        });
    }
    
    /**
     * Image Optimization
     */
    function initImageOptimization() {
        // Convert images to WebP if supported
        function supportsWebP() {
            return new Promise(resolve => {
                const webP = new Image();
                webP.onload = webP.onerror = () => resolve(webP.height === 2);
                webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
            });
        }
        
        supportsWebP().then(supported => {
            if (supported) {
                document.querySelectorAll('img').forEach(img => {
                    if (img.src && !img.src.includes('.webp')) {
                        const webpSrc = img.src.replace(/\.(jpg|jpeg|png)$/i, '.webp');
                        
                        // Test if WebP version exists
                        const testImg = new Image();
                        testImg.onload = () => {
                            img.src = webpSrc;
                        };
                        testImg.src = webpSrc;
                    }
                });
            }
        });
        
        // Add responsive image loading
        function updateImageSizes() {
            const images = document.querySelectorAll('img[data-sizes]');
            images.forEach(img => {
                const sizes = JSON.parse(img.dataset.sizes);
                const windowWidth = window.innerWidth;
                
                let selectedSize = sizes[0];
                for (const size of sizes) {
                    if (windowWidth >= size.minWidth) {
                        selectedSize = size;
                    }
                }
                
                if (img.src !== selectedSize.src) {
                    img.src = selectedSize.src;
                }
            });
        }
        
        window.addEventListener('resize', debounce(updateImageSizes, 250));
        updateImageSizes();
    }
    
    /**
     * Resource Preloading
     */
    function initPreloading() {
        // Preload critical resources
        const criticalResources = [
            { href: 'assets/css/styles.css', as: 'style' },
            { href: 'assets/js/modern-interactions.js', as: 'script' },
            { href: 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap', as: 'style' }
        ];
        
        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = resource.href.startsWith('http') ? resource.href : _base_url_ + resource.href;
            link.as = resource.as;
            if (resource.as === 'style') {
                link.onload = function() {
                    this.rel = 'stylesheet';
                };
            }
            document.head.appendChild(link);
        });
        
        // Prefetch next page resources on hover
        $('a[href^="./"]').on('mouseenter', function() {
            const href = $(this).attr('href');
            if (href && !document.querySelector(`link[rel="prefetch"][href="${href}"]`)) {
                const link = document.createElement('link');
                link.rel = 'prefetch';
                link.href = href;
                document.head.appendChild(link);
            }
        });
    }
    
    /**
     * Service Worker Registration
     */
    function initServiceWorker() {
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    }
    
    /**
     * Performance Analytics
     */
    function initAnalytics() {
        // Measure Core Web Vitals
        if ('PerformanceObserver' in window) {
            // Largest Contentful Paint
            new PerformanceObserver((entryList) => {
                for (const entry of entryList.getEntries()) {
                    console.log('LCP:', entry.startTime);
                }
            }).observe({ entryTypes: ['largest-contentful-paint'] });
            
            // First Input Delay
            new PerformanceObserver((entryList) => {
                for (const entry of entryList.getEntries()) {
                    console.log('FID:', entry.processingStart - entry.startTime);
                }
            }).observe({ entryTypes: ['first-input'] });
            
            // Cumulative Layout Shift
            new PerformanceObserver((entryList) => {
                for (const entry of entryList.getEntries()) {
                    if (!entry.hadRecentInput) {
                        console.log('CLS:', entry.value);
                    }
                }
            }).observe({ entryTypes: ['layout-shift'] });
        }
        
        // Page Load Performance
        window.addEventListener('load', () => {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                console.log('Page Load Time:', perfData.loadEventEnd - perfData.fetchStart);
                console.log('DOM Content Loaded:', perfData.domContentLoadedEventEnd - perfData.fetchStart);
                console.log('First Paint:', performance.getEntriesByType('paint')[0]?.startTime);
            }, 0);
        });
    }
    
    /**
     * Utility Functions
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    /**
     * Progressive Enhancement
     */
    function initProgressiveEnhancement() {
        // Add 'js' class to html element
        document.documentElement.classList.add('js');
        
        // Remove 'no-js' class if present
        document.documentElement.classList.remove('no-js');
        
        // Feature detection
        const features = {
            webp: false,
            intersectionObserver: 'IntersectionObserver' in window,
            serviceWorker: 'serviceWorker' in navigator,
            webGL: !!window.WebGLRenderingContext,
            touchEvents: 'ontouchstart' in window
        };
        
        // Add feature classes
        Object.keys(features).forEach(feature => {
            if (features[feature]) {
                document.documentElement.classList.add(`supports-${feature}`);
            } else {
                document.documentElement.classList.add(`no-${feature}`);
            }
        });
    }
    
    // Initialize progressive enhancement
    initProgressiveEnhancement();
    
    /**
     * Error Handling and Fallbacks
     */
    window.addEventListener('error', (e) => {
        console.error('JavaScript Error:', e.error);
        // Implement fallback behavior if needed
    });
    
    // CSS loading fallback
    setTimeout(() => {
        if (!document.querySelector('link[href*="styles.css"]').sheet) {
            console.warn('CSS failed to load, implementing fallback');
            // Add basic fallback styles
        }
    }, 3000);
});

// Export for use in other scripts
window.PerformanceOptimizations = {
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};
