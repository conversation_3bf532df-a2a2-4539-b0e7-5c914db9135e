<!-- Modern Packages Section -->
<section class="section-modern bg-light" id="home">
	<div class="container">
		<!-- Section Header -->
		<div class="section-header">
			<div class="flex-modern items-center justify-center gap-2 mb-4">
				<div class="badge-modern badge-primary">
					<i class="fas fa-suitcase mr-1"></i>
					Featured Tours
				</div>
			</div>
			<h2 class="section-title">Discover Amazing Destinations</h2>
			<p class="section-subtitle">
				Handpicked tour packages designed to give you the most memorable travel experiences. 
				From adventure seekers to culture enthusiasts, we have something for everyone.
			</p>
		</div>
		
		<!-- Modern Package Grid -->
		<div class="grid-modern grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
			<?php
			$packages = $conn->query("SELECT * FROM `packages` order by rand() limit 3");
				while($row = $packages->fetch_assoc() ):
					$cover='';
					if(is_dir(base_app.'uploads/package_'.$row['id'])){
						$img = scandir(base_app.'uploads/package_'.$row['id']);
						$k = array_search('.',$img);
						if($k !== false)
							unset($img[$k]);
						$k = array_search('..',$img);
						if($k !== false)
							unset($img[$k]);
						$cover = isset($img[2]) ? 'uploads/package_'.$row['id'].'/'.$img[2] : "";
					}
					$row['description'] = strip_tags(stripslashes(html_entity_decode($row['description'])));

					$review = $conn->query("SELECT * FROM `rate_review` where package_id='{$row['id']}'");
					$review_count =$review->num_rows;
					$rate = 0;
					while($r= $review->fetch_assoc()){
						$rate += $r['rate'];
					}
					if($rate > 0 && $review_count > 0)
					$rate = number_format($rate/$review_count,0,"");
			?>
				<div class="card-modern animate-fade-in-up" style="animation-delay: <?php echo ($row['id'] * 0.1) ?>s;">
					<!-- Card Image -->
					<div class="relative overflow-hidden">
						<img class="card-modern-image" src="<?php echo validate_image($cover) ?>" alt="<?php echo $row['title'] ?>">
						
						<!-- Image Overlay -->
						<div class="absolute top-4 left-4">
							<div class="badge-modern badge-success">
								<i class="fas fa-star mr-1"></i>
								<?php echo $rate > 0 ? $rate . '.0' : 'New' ?>
							</div>
						</div>
						
						<!-- Quick Action Buttons -->
						<div class="absolute top-4 right-4 flex-modern flex-col gap-2 opacity-0 transition-normal group-hover:opacity-100">
							<button class="w-10 h-10 bg-white bg-opacity-90 rounded-full flex-modern items-center justify-center shadow-md hover:bg-white transition-fast">
								<i class="fas fa-heart text-danger-500"></i>
							</button>
							<button class="w-10 h-10 bg-white bg-opacity-90 rounded-full flex-modern items-center justify-center shadow-md hover:bg-white transition-fast">
								<i class="fas fa-share-alt text-primary-500"></i>
							</button>
						</div>
						
						<!-- Price Badge -->
						<div class="absolute bottom-4 left-4">
							<div class="bg-white bg-opacity-95 backdrop-blur-sm rounded-lg px-3 py-2 shadow-md">
								<span class="text-sm font-semibold text-secondary-600">Starting from</span>
								<div class="text-lg font-bold text-primary-600">$299</div>
							</div>
						</div>
					</div>
					
					<!-- Card Content -->
					<div class="card-modern-body">
						<!-- Title and Rating -->
						<div class="flex-modern items-start justify-between mb-3">
							<h3 class="text-h5 font-bold text-secondary-900 line-clamp-2 flex-1">
								<?php echo $row['title'] ?>
							</h3>
						</div>
						
						<!-- Rating Stars -->
						<div class="flex-modern items-center gap-2 mb-3">
							<div class="flex-modern items-center">
								<?php for($i = 1; $i <= 5; $i++): ?>
									<i class="fas fa-star text-sm <?php echo $i <= $rate ? 'text-warning-500' : 'text-secondary-300' ?>"></i>
								<?php endfor; ?>
							</div>
							<span class="text-sm text-secondary-600">
								(<?php echo $review_count ?> reviews)
							</span>
						</div>
						
						<!-- Description -->
						<p class="text-body-sm text-secondary-600 mb-4 line-clamp-3">
							<?php echo $row['description'] ?>
						</p>
						
						<!-- Features -->
						<div class="flex-modern items-center gap-4 mb-4 text-sm text-secondary-600">
							<div class="flex-modern items-center gap-1">
								<i class="fas fa-clock text-primary-500"></i>
								<span>5 Days</span>
							</div>
							<div class="flex-modern items-center gap-1">
								<i class="fas fa-users text-primary-500"></i>
								<span>Max 12</span>
							</div>
							<div class="flex-modern items-center gap-1">
								<i class="fas fa-map-marker-alt text-primary-500"></i>
								<span>Adventure</span>
							</div>
						</div>
						
						<!-- Action Button -->
						<a href="./?page=view_package&id=<?php echo md5($row['id']) ?>" 
						   class="btn-modern btn-modern-primary w-full justify-center">
							<i class="fas fa-eye mr-2"></i>
							View Details
						</a>
					</div>
				</div>
			<?php endwhile; ?>
		</div>
		
		<!-- Call to Action -->
		<div class="text-center">
			<a href="./?page=packages" class="btn-modern btn-modern-outline btn-modern-lg">
				<i class="fas fa-compass mr-2"></i>
				Explore All Packages
			</a>
		</div>
	</div>
</section>

<style>
/* Modern Package Card Styles */
.card-modern {
	group: true;
	position: relative;
}

.card-modern:hover {
	transform: translateY(-8px);
}

.card-modern .absolute {
	position: absolute;
}

.card-modern .top-4 { top: 1rem; }
.card-modern .left-4 { left: 1rem; }
.card-modern .right-4 { right: 1rem; }
.card-modern .bottom-4 { bottom: 1rem; }

.card-modern .w-10 { width: 2.5rem; }
.card-modern .h-10 { height: 2.5rem; }

.card-modern .bg-opacity-90 { background-color: rgba(255, 255, 255, 0.9); }
.card-modern .bg-opacity-95 { background-color: rgba(255, 255, 255, 0.95); }

.card-modern .backdrop-blur-sm {
	backdrop-filter: blur(4px);
	-webkit-backdrop-filter: blur(4px);
}

.card-modern .line-clamp-2 {
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.card-modern .line-clamp-3 {
	display: -webkit-box;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.card-modern .group-hover\:opacity-100:hover {
	opacity: 1;
}

.card-modern .relative { position: relative; }
.card-modern .overflow-hidden { overflow: hidden; }

/* Additional responsive utilities */
@media (max-width: 768px) {
	.grid-modern.md\:grid-cols-2 {
		grid-template-columns: repeat(1, 1fr);
	}
}

@media (min-width: 769px) and (max-width: 1024px) {
	.grid-modern.md\:grid-cols-2 {
		grid-template-columns: repeat(2, 1fr);
	}
}

@media (min-width: 1025px) {
	.grid-modern.lg\:grid-cols-3 {
		grid-template-columns: repeat(3, 1fr);
	}
}

/* Additional utility classes */
.w-full { width: 100%; }
.text-sm { font-size: 0.875rem; }
.text-lg { font-size: 1.125rem; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-full { border-radius: 9999px; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mr-1 { margin-right: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }
.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-4 { gap: 1rem; }
.gap-8 { gap: 2rem; }
.mb-12 { margin-bottom: 3rem; }
.flex-1 { flex: 1; }
.opacity-0 { opacity: 0; }
</style>
