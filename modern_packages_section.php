<!-- Clean Packages Section -->
<section class="py-5 bg-light" id="home">
	<div class="container">
		<!-- Section Header -->
		<div class="text-center mb-5">
			<h2 class="display-4 fw-bold text-primary mb-3">Featured Tour Packages</h2>
			<p class="lead text-muted">
				Discover amazing destinations with our carefully curated tour packages
			</p>
		</div>

		<!-- Clean Package Grid -->
		<div class="row g-4 mb-5">
			<?php
			$packages = $conn->query("SELECT * FROM `packages` order by rand() limit 3");
				while($row = $packages->fetch_assoc() ):
					$cover='';
					if(is_dir(base_app.'uploads/package_'.$row['id'])){
						$img = scandir(base_app.'uploads/package_'.$row['id']);
						$k = array_search('.',$img);
						if($k !== false)
							unset($img[$k]);
						$k = array_search('..',$img);
						if($k !== false)
							unset($img[$k]);
						$cover = isset($img[2]) ? 'uploads/package_'.$row['id'].'/'.$img[2] : "";
					}
					$row['description'] = strip_tags(stripslashes(html_entity_decode($row['description'])));

					$review = $conn->query("SELECT * FROM `rate_review` where package_id='{$row['id']}'");
					$review_count =$review->num_rows;
					$rate = 0;
					while($r= $review->fetch_assoc()){
						$rate += $r['rate'];
					}
					if($rate > 0 && $review_count > 0)
					$rate = number_format($rate/$review_count,0,"");
			?>
				<div class="col-lg-4 col-md-6">
					<div class="card h-100 shadow-sm border-0 rounded-3 overflow-hidden">
						<!-- Card Image -->
						<div class="position-relative">
							<img src="<?php echo validate_image($cover) ?>"
								 class="card-img-top"
								 alt="<?php echo $row['title'] ?>"
								 style="height: 250px; object-fit: cover;">

							<!-- Rating Badge -->
							<div class="position-absolute top-0 start-0 m-3">
								<span class="badge bg-success rounded-pill">
									<i class="fas fa-star me-1"></i>
									<?php echo $rate > 0 ? $rate . '.0' : 'New' ?>
								</span>
							</div>
						</div>

						<!-- Card Body -->
						<div class="card-body p-4">
							<h5 class="card-title fw-bold mb-2"><?php echo $row['title'] ?></h5>

							<!-- Rating Stars -->
							<div class="d-flex align-items-center mb-3">
								<div class="me-2">
									<?php for($i = 1; $i <= 5; $i++): ?>
										<i class="fas fa-star <?php echo $i <= $rate ? 'text-warning' : 'text-muted' ?>"></i>
									<?php endfor; ?>
								</div>
								<small class="text-muted">(<?php echo $review_count ?> reviews)</small>
							</div>

							<!-- Description -->
							<p class="card-text text-muted mb-3" style="display: -webkit-box; -webkit-line-clamp: 3; -webkit-box-orient: vertical; overflow: hidden;">
								<?php echo $row['description'] ?>
							</p>

							<!-- Features -->
							<div class="d-flex justify-content-between text-muted small mb-3">
								<span><i class="fas fa-clock me-1"></i>5 Days</span>
								<span><i class="fas fa-users me-1"></i>Max 12</span>
								<span><i class="fas fa-map-marker-alt me-1"></i>Adventure</span>
							</div>
						</div>

						<!-- Card Footer -->
						<div class="card-footer bg-transparent border-0 p-4 pt-0">
							<a href="./?page=view_package&id=<?php echo md5($row['id']) ?>"
							   class="btn btn-primary w-100 rounded-pill">
								<i class="fas fa-eye me-2"></i>View Details
							</a>
						</div>
					</div>
				</div>
			<?php endwhile; ?>
		</div>

		<!-- Call to Action -->
		<div class="text-center">
			<a href="./?page=packages" class="btn btn-outline-primary btn-lg rounded-pill px-5">
				<i class="fas fa-compass me-2"></i>
				Explore All Packages
			</a>
		</div>
	</div>
</section>

<style>
/* Simple hover effects for cards */
.card {
	transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
	transform: translateY(-5px);
	box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

/* Smooth button transitions */
.btn {
	transition: all 0.2s ease;
}

.btn:hover {
	transform: translateY(-1px);
}
</style>
