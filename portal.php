
<style>
/* Modern Hero Section Styles */
.hero-modern {
	background: var(--gradient-hero);
	position: relative;
	overflow: hidden;
	min-height: 100vh;
	display: flex;
	align-items: center;
	justify-content: center;
}

.hero-modern::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-image: url('<?php echo validate_image($_settings->info('cover')) ?>');
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	opacity: 0.3;
	z-index: 1;
}

.hero-modern::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(14, 165, 233, 0.8) 0%, rgba(168, 85, 247, 0.8) 50%, rgba(14, 165, 233, 0.9) 100%);
	z-index: 2;
}

.hero-content {
	position: relative;
	z-index: 3;
	text-align: center;
	color: white;
	max-width: 900px;
	padding: var(--space-8);
}

.hero-badge {
	display: inline-flex;
	align-items: center;
	gap: var(--space-2);
	background: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
	padding: var(--space-2) var(--space-4);
	border-radius: var(--radius-full);
	font-size: 0.875rem;
	font-weight: 600;
	margin-bottom: var(--space-6);
	animation: fadeInDown 0.8s ease-out;
}

.hero-title {
	font-size: clamp(2.5rem, 6vw, 4.5rem);
	font-weight: 800;
	line-height: 1.1;
	margin-bottom: var(--space-6);
	animation: fadeInUp 0.8s ease-out 0.2s both;
}

.hero-subtitle {
	font-size: clamp(1.125rem, 2.5vw, 1.5rem);
	line-height: 1.6;
	opacity: 0.95;
	margin-bottom: var(--space-8);
	max-width: 600px;
	margin-left: auto;
	margin-right: auto;
	animation: fadeInUp 0.8s ease-out 0.4s both;
}

.hero-cta {
	display: flex;
	gap: var(--space-4);
	justify-content: center;
	flex-wrap: wrap;
	animation: fadeInUp 0.8s ease-out 0.6s both;
}

.hero-stats {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
	gap: var(--space-6);
	margin-top: var(--space-12);
	animation: fadeInUp 0.8s ease-out 0.8s both;
}

.hero-stat {
	text-align: center;
	background: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
	padding: var(--space-4);
	border-radius: var(--radius-xl);
}

.hero-stat-number {
	font-size: 2rem;
	font-weight: 800;
	display: block;
	margin-bottom: var(--space-1);
}

.hero-stat-label {
	font-size: 0.875rem;
	opacity: 0.9;
	font-weight: 500;
}

/* Floating Elements Animation */
.floating-element {
	position: absolute;
	animation: float 6s ease-in-out infinite;
}

.floating-element:nth-child(1) {
	top: 20%;
	left: 10%;
	animation-delay: 0s;
}

.floating-element:nth-child(2) {
	top: 60%;
	right: 15%;
	animation-delay: 2s;
}

.floating-element:nth-child(3) {
	bottom: 20%;
	left: 20%;
	animation-delay: 4s;
}

@keyframes float {
	0%, 100% {
		transform: translateY(0px) rotate(0deg);
	}
	50% {
		transform: translateY(-20px) rotate(5deg);
	}
}

/* Responsive Adjustments */
@media (max-width: 768px) {
	.hero-cta {
		flex-direction: column;
		align-items: center;
	}

	.hero-stats {
		grid-template-columns: repeat(2, 1fr);
		gap: var(--space-4);
	}

	.hero-content {
		padding: var(--space-6) var(--space-4);
	}
}
</style>

<!-- Modern Hero Section -->
<header class="hero-modern">
	<!-- Floating Decorative Elements -->
	<div class="floating-element">
		<div class="w-16 h-16 bg-white bg-opacity-10 rounded-full flex items-center justify-center">
			<i class="fas fa-mountain text-white text-xl"></i>
		</div>
	</div>
	<div class="floating-element">
		<div class="w-12 h-12 bg-white bg-opacity-10 rounded-full flex items-center justify-center">
			<i class="fas fa-camera text-white"></i>
		</div>
	</div>
	<div class="floating-element">
		<div class="w-14 h-14 bg-white bg-opacity-10 rounded-full flex items-center justify-center">
			<i class="fas fa-map-marked-alt text-white text-lg"></i>
		</div>
	</div>

	<div class="hero-content">
		<!-- Hero Badge -->
		<div class="hero-badge">
			<i class="fas fa-star text-yellow-300"></i>
			<span>Trusted by 10,000+ Travelers</span>
		</div>

		<!-- Hero Title -->
		<h1 class="hero-title">
			Discover Amazing
			<span class="block">Travel Experiences</span>
		</h1>

		<!-- Hero Subtitle -->
		<p class="hero-subtitle">
			Embark on unforgettable journeys with our carefully curated tour packages.
			From breathtaking landscapes to cultural adventures, we make your travel dreams come true.
		</p>

		<!-- Hero CTA Buttons -->
		<div class="hero-cta">
			<a href="#home" class="btn-modern btn-modern-primary btn-modern-lg">
				<i class="fas fa-compass mr-2"></i>
				Explore Packages
			</a>
			<a href="#about" class="btn-modern btn-modern-outline btn-modern-lg" style="border-color: white; color: white;">
				<i class="fas fa-play mr-2"></i>
				Watch Video
			</a>
		</div>

		<!-- Hero Stats -->
		<div class="hero-stats">
			<div class="hero-stat">
				<span class="hero-stat-number">500+</span>
				<span class="hero-stat-label">Destinations</span>
			</div>
			<div class="hero-stat">
				<span class="hero-stat-number">10K+</span>
				<span class="hero-stat-label">Happy Travelers</span>
			</div>
			<div class="hero-stat">
				<span class="hero-stat-number">4.9</span>
				<span class="hero-stat-label">Rating</span>
			</div>
			<div class="hero-stat">
				<span class="hero-stat-number">24/7</span>
				<span class="hero-stat-label">Support</span>
			</div>
		</div>
	</div>
</header>
<?php include 'modern_packages_section.php'; ?>
<!-- Modern About Section -->
<section class="section-modern bg-white" id="about">
	<div class="container">
		<!-- Section Header -->
		<div class="section-header">
			<div class="flex-modern items-center justify-center gap-2 mb-4">
				<div class="badge-modern badge-secondary">
					<i class="fas fa-info-circle mr-1"></i>
					Our Story
				</div>
			</div>
			<h2 class="section-title">About TourismPro</h2>
			<p class="section-subtitle">
				Crafting unforgettable travel experiences for adventurers worldwide since 2010.
			</p>
		</div>

		<!-- About Content Grid -->
		<div class="grid-modern grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
			<!-- About Text -->
			<div class="space-y-6">
				<div class="card-modern">
					<div class="card-modern-body">
						<?php
						$about_content = file_get_contents(base_app.'about.html');
						if($about_content) {
							echo $about_content;
						} else {
							echo '<p class="text-body">We are passionate travel experts dedicated to creating extraordinary journeys that inspire, educate, and transform. With over a decade of experience in the tourism industry, we have helped thousands of travelers discover the beauty and wonder of destinations around the globe.</p>
							<p class="text-body">Our team of experienced travel consultants works tirelessly to curate unique experiences that go beyond typical tourist attractions. We believe that travel should be transformative, connecting you with local cultures, breathtaking landscapes, and unforgettable memories.</p>';
						}
						?>
					</div>
				</div>
			</div>

			<!-- Stats Grid -->
			<div class="grid-modern grid-cols-2 gap-6">
				<div class="card-modern text-center">
					<div class="card-modern-body">
						<div class="text-display-2 text-primary-600 mb-2">500+</div>
						<div class="text-h6 text-secondary-600">Destinations</div>
					</div>
				</div>
				<div class="card-modern text-center">
					<div class="card-modern-body">
						<div class="text-display-2 text-primary-600 mb-2">10K+</div>
						<div class="text-h6 text-secondary-600">Happy Travelers</div>
					</div>
				</div>
				<div class="card-modern text-center">
					<div class="card-modern-body">
						<div class="text-display-2 text-primary-600 mb-2">15</div>
						<div class="text-h6 text-secondary-600">Years Experience</div>
					</div>
				</div>
				<div class="card-modern text-center">
					<div class="card-modern-body">
						<div class="text-display-2 text-primary-600 mb-2">4.9</div>
						<div class="text-h6 text-secondary-600">Average Rating</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Features Grid -->
		<div class="grid-modern grid-cols-1 md:grid-cols-3 gap-8">
			<div class="card-modern text-center">
				<div class="card-modern-body">
					<div class="w-16 h-16 bg-gradient-primary rounded-2xl flex-modern items-center justify-center mx-auto mb-4">
						<i class="fas fa-globe-americas text-white text-2xl"></i>
					</div>
					<h3 class="text-h5 mb-3">Global Reach</h3>
					<p class="text-body-sm text-secondary-600">
						Explore destinations across all continents with our extensive network of local partners and guides.
					</p>
				</div>
			</div>

			<div class="card-modern text-center">
				<div class="card-modern-body">
					<div class="w-16 h-16 bg-gradient-accent rounded-2xl flex-modern items-center justify-center mx-auto mb-4">
						<i class="fas fa-heart text-white text-2xl"></i>
					</div>
					<h3 class="text-h5 mb-3">Personalized Service</h3>
					<p class="text-body-sm text-secondary-600">
						Every journey is tailored to your preferences, ensuring a unique and memorable travel experience.
					</p>
				</div>
			</div>

			<div class="card-modern text-center">
				<div class="card-modern-body">
					<div class="w-16 h-16 bg-gradient-secondary rounded-2xl flex-modern items-center justify-center mx-auto mb-4">
						<i class="fas fa-shield-alt text-white text-2xl"></i>
					</div>
					<h3 class="text-h5 mb-3">Safe & Secure</h3>
					<p class="text-body-sm text-secondary-600">
						Travel with confidence knowing that your safety and security are our top priorities at every step.
					</p>
				</div>
			</div>
		</div>
	</div>
</section>

<!-- Modern Testimonials Section -->
<section class="section-modern bg-secondary-50" id="testimonials">
	<div class="container">
		<!-- Section Header -->
		<div class="section-header">
			<div class="flex-modern items-center justify-center gap-2 mb-4">
				<div class="badge-modern badge-accent">
					<i class="fas fa-quote-left mr-1"></i>
					Testimonials
				</div>
			</div>
			<h2 class="section-title">What Our Travelers Say</h2>
			<p class="section-subtitle">
				Don't just take our word for it. Here's what our amazing travelers have to say about their experiences with us.
			</p>
		</div>

		<!-- Testimonials Grid -->
		<div class="grid-modern grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
			<!-- Testimonial 1 -->
			<div class="card-modern">
				<div class="card-modern-body">
					<div class="flex-modern items-center mb-4">
						<?php for($i = 1; $i <= 5; $i++): ?>
							<i class="fas fa-star text-warning-500"></i>
						<?php endfor; ?>
					</div>
					<blockquote class="text-body-sm text-secondary-700 mb-6 italic">
						"TourismPro made our dream vacation to Bali absolutely perfect. Every detail was taken care of, and the local guides were incredible. We can't wait to book our next adventure!"
					</blockquote>
					<div class="flex-modern items-center gap-4">
						<div class="w-12 h-12 bg-gradient-primary rounded-full flex-modern items-center justify-center">
							<span class="text-white font-bold">SJ</span>
						</div>
						<div>
							<div class="font-semibold text-secondary-900">Sarah Johnson</div>
							<div class="text-sm text-secondary-600">Adventure Enthusiast</div>
						</div>
					</div>
				</div>
			</div>

			<!-- Testimonial 2 -->
			<div class="card-modern">
				<div class="card-modern-body">
					<div class="flex-modern items-center mb-4">
						<?php for($i = 1; $i <= 5; $i++): ?>
							<i class="fas fa-star text-warning-500"></i>
						<?php endfor; ?>
					</div>
					<blockquote class="text-body-sm text-secondary-700 mb-6 italic">
						"The cultural tour of Japan exceeded all our expectations. The attention to detail and personalized service made this trip truly unforgettable. Highly recommended!"
					</blockquote>
					<div class="flex-modern items-center gap-4">
						<div class="w-12 h-12 bg-gradient-accent rounded-full flex-modern items-center justify-center">
							<span class="text-white font-bold">MC</span>
						</div>
						<div>
							<div class="font-semibold text-secondary-900">Michael Chen</div>
							<div class="text-sm text-secondary-600">Culture Explorer</div>
						</div>
					</div>
				</div>
			</div>

			<!-- Testimonial 3 -->
			<div class="card-modern">
				<div class="card-modern-body">
					<div class="flex-modern items-center mb-4">
						<?php for($i = 1; $i <= 5; $i++): ?>
							<i class="fas fa-star text-warning-500"></i>
						<?php endfor; ?>
					</div>
					<blockquote class="text-body-sm text-secondary-700 mb-6 italic">
						"From booking to return, everything was seamless. The 24/7 support gave us peace of mind, and the experiences were beyond amazing. Thank you TourismPro!"
					</blockquote>
					<div class="flex-modern items-center gap-4">
						<div class="w-12 h-12 bg-gradient-secondary rounded-full flex-modern items-center justify-center">
							<span class="text-white font-bold">ER</span>
						</div>
						<div>
							<div class="font-semibold text-secondary-900">Emily Rodriguez</div>
							<div class="text-sm text-secondary-600">Family Traveler</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Trust Indicators -->
		<div class="mt-16 text-center">
			<div class="grid-modern grid-cols-2 md:grid-cols-4 gap-8">
				<div class="text-center">
					<div class="text-display-2 text-primary-600 font-bold mb-2">4.9</div>
					<div class="text-sm text-secondary-600">Average Rating</div>
				</div>
				<div class="text-center">
					<div class="text-display-2 text-primary-600 font-bold mb-2">10K+</div>
					<div class="text-sm text-secondary-600">Happy Customers</div>
				</div>
				<div class="text-center">
					<div class="text-display-2 text-primary-600 font-bold mb-2">500+</div>
					<div class="text-sm text-secondary-600">Destinations</div>
				</div>
				<div class="text-center">
					<div class="text-display-2 text-primary-600 font-bold mb-2">15</div>
					<div class="text-sm text-secondary-600">Years Experience</div>
				</div>
			</div>
		</div>
	</div>
</section>

<!-- Modern Contact Section -->
<section class="section-modern bg-gradient-hero" id="contact">
	<div class="container">
		<!-- Section Header -->
		<div class="section-header text-center text-white">
			<div class="flex-modern items-center justify-center gap-2 mb-4">
				<div class="badge-modern" style="background: rgba(255, 255, 255, 0.1); color: white; border: 1px solid rgba(255, 255, 255, 0.2);">
					<i class="fas fa-envelope mr-1"></i>
					Get In Touch
				</div>
			</div>
			<h2 class="section-title text-white">Let's Start Your Journey</h2>
			<p class="section-subtitle text-white opacity-90">
				Ready to explore the world? Send us a message and our travel experts will help you plan your perfect adventure.
			</p>
		</div>

		<!-- Contact Content Grid -->
		<div class="grid-modern grid-cols-1 lg:grid-cols-2 gap-12 items-center">
			<!-- Contact Info -->
			<div class="space-y-8">
				<div class="glass rounded-2xl p-8">
					<h3 class="text-h4 text-white mb-6">Why Choose Us?</h3>
					<div class="space-y-6">
						<div class="flex-modern items-start gap-4">
							<div class="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex-modern items-center justify-center flex-shrink-0">
								<i class="fas fa-award text-white text-lg"></i>
							</div>
							<div>
								<h4 class="text-h6 text-white mb-2">Expert Guidance</h4>
								<p class="text-white opacity-80">Our experienced travel consultants provide personalized recommendations.</p>
							</div>
						</div>
						<div class="flex-modern items-start gap-4">
							<div class="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex-modern items-center justify-center flex-shrink-0">
								<i class="fas fa-shield-alt text-white text-lg"></i>
							</div>
							<div>
								<h4 class="text-h6 text-white mb-2">Safe & Secure</h4>
								<p class="text-white opacity-80">Your safety is our priority with comprehensive travel insurance.</p>
							</div>
						</div>
						<div class="flex-modern items-start gap-4">
							<div class="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex-modern items-center justify-center flex-shrink-0">
								<i class="fas fa-clock text-white text-lg"></i>
							</div>
							<div>
								<h4 class="text-h6 text-white mb-2">24/7 Support</h4>
								<p class="text-white opacity-80">Round-the-clock assistance throughout your journey.</p>
							</div>
						</div>
					</div>
				</div>

				<!-- Contact Details -->
				<div class="glass rounded-2xl p-8">
					<h3 class="text-h4 text-white mb-6">Contact Information</h3>
					<div class="space-y-4">
						<div class="flex-modern items-center gap-3">
							<i class="fas fa-phone text-white text-lg"></i>
							<span class="text-white">+****************</span>
						</div>
						<div class="flex-modern items-center gap-3">
							<i class="fas fa-envelope text-white text-lg"></i>
							<span class="text-white"><EMAIL></span>
						</div>
						<div class="flex-modern items-center gap-3">
							<i class="fas fa-map-marker-alt text-white text-lg"></i>
							<span class="text-white">123 Travel Street, Adventure City, AC 12345</span>
						</div>
					</div>
				</div>
			</div>

			<!-- Modern Contact Form -->
			<div class="glass rounded-2xl p-8">
				<form id="contactForm" class="form-modern">
					<div class="grid-modern grid-cols-1 md:grid-cols-2 gap-6">
						<div class="form-group-modern">
							<label class="form-label-modern text-white">Full Name</label>
							<input class="form-input-modern" id="name" name="name" type="text" placeholder="Enter your full name" required />
						</div>
						<div class="form-group-modern">
							<label class="form-label-modern text-white">Email Address</label>
							<input class="form-input-modern" id="email" name="email" type="email" placeholder="Enter your email" required />
						</div>
					</div>

					<div class="form-group-modern">
						<label class="form-label-modern text-white">Subject</label>
						<input class="form-input-modern" id="subject" name="subject" type="text" placeholder="What's this about?" required />
					</div>

					<div class="form-group-modern">
						<label class="form-label-modern text-white">Message</label>
						<textarea class="form-input-modern form-textarea-modern" id="message" name="message" placeholder="Tell us about your dream destination..." required></textarea>
					</div>

					<button class="btn-modern btn-modern-primary btn-modern-lg w-full" id="submitButton" type="submit">
						<i class="fas fa-paper-plane mr-2"></i>
						Send Message
					</button>
				</form>
			</div>
		</div>
	</div>
</section>
<script>
$(function(){
	$('#contactForm').submit(function(e){
		e.preventDefault()
		$.ajax({
			url:_base_url_+"classes/Master.php?f=save_inquiry",
			method:"POST",
			data:$(this).serialize(),
			dataType:"json",
			error:err=>{
				console.log(err)
				alert_toast("an error occured",'error')
				end_loader()
			},
			success:function(resp){
				if(typeof resp == 'object' && resp.status == 'success'){
					alert_toast("Inquiry sent",'success')
					$('#contactForm').get(0).reset()
				}else{
					console.log(resp)
					alert_toast("an error occured",'error')
					end_loader()
				}
			}
		})
	})
})
</script>