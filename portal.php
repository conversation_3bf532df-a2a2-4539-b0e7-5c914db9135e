
<style>
/* Clean Hero Section Styles */
.masthead {
	background: linear-gradient(135deg, #0ea5e9 0%, #8b5cf6 50%, #0ea5e9 100%);
	background-image: url('<?php echo validate_image($_settings->info('cover')) ?>');
	background-size: cover;
	background-position: center;
	background-blend-mode: overlay;
	position: relative;
	min-height: 100vh;
}

.masthead::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.4);
	z-index: 1;
}

.masthead .container {
	position: relative;
	z-index: 2;
}

/* Smooth animations */
.masthead .badge {
	animation: fadeInDown 0.8s ease-out;
}

.masthead h1 {
	animation: fadeInUp 0.8s ease-out 0.2s both;
}

.masthead .lead {
	animation: fadeInUp 0.8s ease-out 0.4s both;
}

.masthead .d-flex {
	animation: fadeInUp 0.8s ease-out 0.6s both;
}

.masthead .row {
	animation: fadeInUp 0.8s ease-out 0.8s both;
}

@keyframes fadeInDown {
	from {
		opacity: 0;
		transform: translateY(-30px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(30px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

/* Button hover effects */
.btn {
	transition: all 0.3s ease;
}

.btn:hover {
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}
</style>

<!-- Clean Hero Section -->
<header class="masthead">
	<div class="container">
		<div class="row align-items-center min-vh-100">
			<div class="col-lg-8 mx-auto text-center text-white">
				<!-- Hero Badge -->
				<div class="mb-4">
					<span class="badge bg-light text-primary rounded-pill px-3 py-2">
						<i class="fas fa-star me-2"></i>
						Trusted by 10,000+ Travelers
					</span>
				</div>

				<!-- Hero Title -->
				<h1 class="display-2 fw-bold mb-4">
					Discover Amazing
					<span class="text-warning">Travel Experiences</span>
				</h1>

				<!-- Hero Subtitle -->
				<p class="lead mb-5 fs-4">
					Embark on unforgettable journeys with our carefully curated tour packages.
					From breathtaking landscapes to cultural adventures, we make your travel dreams come true.
				</p>

				<!-- Hero CTA Buttons -->
				<div class="d-flex flex-column flex-sm-row gap-3 justify-content-center mb-5">
					<a href="#home" class="btn btn-warning btn-lg rounded-pill px-5">
						<i class="fas fa-compass me-2"></i>
						Explore Packages
					</a>
					<a href="#about" class="btn btn-outline-light btn-lg rounded-pill px-5">
						<i class="fas fa-play me-2"></i>
						Watch Video
					</a>
				</div>

				<!-- Hero Stats -->
				<div class="row g-4 mt-4">
					<div class="col-6 col-md-3">
						<div class="text-center">
							<div class="display-6 fw-bold text-warning">500+</div>
							<div class="small">Destinations</div>
						</div>
					</div>
					<div class="col-6 col-md-3">
						<div class="text-center">
							<div class="display-6 fw-bold text-warning">10K+</div>
							<div class="small">Happy Travelers</div>
						</div>
					</div>
					<div class="col-6 col-md-3">
						<div class="text-center">
							<div class="display-6 fw-bold text-warning">4.9</div>
							<div class="small">Rating</div>
						</div>
					</div>
					<div class="col-6 col-md-3">
						<div class="text-center">
							<div class="display-6 fw-bold text-warning">24/7</div>
							<div class="small">Support</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</header>
<?php include 'modern_packages_section.php'; ?>
<!-- About Section -->
<section class="py-5 bg-white" id="about">
	<div class="container">
		<!-- Section Header -->
		<div class="text-center mb-5">
			<h2 class="display-4 fw-bold text-primary mb-3">About TourismPro</h2>
			<p class="lead text-muted">
				Crafting unforgettable travel experiences for adventurers worldwide since 2010.
			</p>
		</div>

		<!-- About Content -->
		<div class="row align-items-center mb-5">
			<!-- About Text -->
			<div class="col-lg-8">
				<div class="card border-0 shadow-sm">
					<div class="card-body p-4">
						<?php
						$about_content = file_get_contents(base_app.'about.html');
						if($about_content) {
							echo $about_content;
						} else {
							echo '<p>We are passionate travel experts dedicated to creating extraordinary journeys that inspire, educate, and transform. With over a decade of experience in the tourism industry, we have helped thousands of travelers discover the beauty and wonder of destinations around the globe.</p>
							<p>Our team of experienced travel consultants works tirelessly to curate unique experiences that go beyond typical tourist attractions. We believe that travel should be transformative, connecting you with local cultures, breathtaking landscapes, and unforgettable memories.</p>';
						}
						?>
					</div>
				</div>
			</div>

			<!-- Stats -->
			<div class="col-lg-4">
				<div class="row g-3">
					<div class="col-6">
						<div class="card text-center border-0 shadow-sm">
							<div class="card-body">
								<div class="display-6 fw-bold text-primary">500+</div>
								<div class="small text-muted">Destinations</div>
							</div>
						</div>
					</div>
					<div class="col-6">
						<div class="card text-center border-0 shadow-sm">
							<div class="card-body">
								<div class="display-6 fw-bold text-primary">10K+</div>
								<div class="small text-muted">Happy Travelers</div>
							</div>
						</div>
					</div>
					<div class="col-6">
						<div class="card text-center border-0 shadow-sm">
							<div class="card-body">
								<div class="display-6 fw-bold text-primary">15</div>
								<div class="small text-muted">Years Experience</div>
							</div>
						</div>
					</div>
					<div class="col-6">
						<div class="card text-center border-0 shadow-sm">
							<div class="card-body">
								<div class="display-6 fw-bold text-primary">4.9</div>
								<div class="small text-muted">Average Rating</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Features -->
		<div class="row g-4">
			<div class="col-md-4">
				<div class="card text-center border-0 shadow-sm h-100">
					<div class="card-body p-4">
						<div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
							<i class="fas fa-globe-americas text-white fs-4"></i>
						</div>
						<h5 class="card-title">Global Reach</h5>
						<p class="card-text text-muted">
							Explore destinations across all continents with our extensive network of local partners and guides.
						</p>
					</div>
				</div>
			</div>

			<div class="col-md-4">
				<div class="card text-center border-0 shadow-sm h-100">
					<div class="card-body p-4">
						<div class="bg-success rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
							<i class="fas fa-heart text-white fs-4"></i>
						</div>
						<h5 class="card-title">Personalized Service</h5>
						<p class="card-text text-muted">
							Every journey is tailored to your preferences, ensuring a unique and memorable travel experience.
						</p>
					</div>
				</div>
			</div>

			<div class="col-md-4">
				<div class="card text-center border-0 shadow-sm h-100">
					<div class="card-body p-4">
						<div class="bg-warning rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
							<i class="fas fa-shield-alt text-white fs-4"></i>
						</div>
						<h5 class="card-title">Safe & Secure</h5>
						<p class="card-text text-muted">
							Travel with confidence knowing that your safety and security are our top priorities at every step.
						</p>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>


<!-- Contact Section -->
<section class="py-5 bg-primary text-white" id="contact">
	<div class="container">
		<!-- Section Header -->
		<div class="text-center mb-5">
			<h2 class="display-4 fw-bold mb-3">Let's Start Your Journey</h2>
			<p class="lead">
				Ready to explore the world? Send us a message and our travel experts will help you plan your perfect adventure.
			</p>
		</div>

		<!-- Contact Content -->
		<div class="row">
			<!-- Contact Info -->
			<div class="col-lg-6 mb-4">
				<div class="card bg-transparent border-light">
					<div class="card-body">
						<h3 class="h4 mb-4">Why Choose Us?</h3>
						<div class="row g-4">
							<div class="col-12">
								<div class="d-flex align-items-start">
									<div class="bg-light bg-opacity-25 rounded-circle p-3 me-3">
										<i class="fas fa-award text-warning"></i>
									</div>
									<div>
										<h5>Expert Guidance</h5>
										<p class="mb-0 opacity-75">Our experienced travel consultants provide personalized recommendations.</p>
									</div>
								</div>
							</div>
							<div class="col-12">
								<div class="d-flex align-items-start">
									<div class="bg-light bg-opacity-25 rounded-circle p-3 me-3">
										<i class="fas fa-shield-alt text-warning"></i>
									</div>
									<div>
										<h5>Safe & Secure</h5>
										<p class="mb-0 opacity-75">Your safety is our priority with comprehensive travel insurance.</p>
									</div>
								</div>
							</div>
							<div class="col-12">
								<div class="d-flex align-items-start">
									<div class="bg-light bg-opacity-25 rounded-circle p-3 me-3">
										<i class="fas fa-clock text-warning"></i>
									</div>
									<div>
										<h5>24/7 Support</h5>
										<p class="mb-0 opacity-75">Round-the-clock assistance throughout your journey.</p>
									</div>
								</div>
							</div>
						</div>

						<!-- Contact Details -->
						<div class="mt-4 pt-4 border-top border-light border-opacity-25">
							<h5 class="mb-3">Contact Information</h5>
							<div class="d-flex align-items-center mb-2">
								<i class="fas fa-phone me-3"></i>
								<span>+****************</span>
							</div>
							<div class="d-flex align-items-center mb-2">
								<i class="fas fa-envelope me-3"></i>
								<span><EMAIL></span>
							</div>
							<div class="d-flex align-items-center">
								<i class="fas fa-map-marker-alt me-3"></i>
								<span>123 Travel Street, Adventure City, AC 12345</span>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- Contact Form -->
			<div class="col-lg-6">
				<div class="card bg-light bg-opacity-10 border-light">
					<div class="card-body">
						<form id="contactForm">
							<div class="row g-3">
								<div class="col-md-6">
									<label class="form-label text-white">Full Name</label>
									<input class="form-control" id="name" name="name" type="text" placeholder="Enter your full name" required />
								</div>
								<div class="col-md-6">
									<label class="form-label text-white">Email Address</label>
									<input class="form-control" id="email" name="email" type="email" placeholder="Enter your email" required />
								</div>
								<div class="col-12">
									<label class="form-label text-white">Subject</label>
									<input class="form-control" id="subject" name="subject" type="text" placeholder="What's this about?" required />
								</div>
								<div class="col-12">
									<label class="form-label text-white">Message</label>
									<textarea class="form-control" id="message" name="message" rows="5" placeholder="Tell us about your dream destination..." required></textarea>
								</div>
								<div class="col-12">
									<button class="btn btn-warning btn-lg w-100" id="submitButton" type="submit">
										<i class="fas fa-paper-plane me-2"></i>
										Send Message
									</button>
								</div>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>
<script>
$(function(){
	$('#contactForm').submit(function(e){
		e.preventDefault()
		$.ajax({
			url:_base_url_+"classes/Master.php?f=save_inquiry",
			method:"POST",
			data:$(this).serialize(),
			dataType:"json",
			error:err=>{
				console.log(err)
				alert_toast("an error occured",'error')
				end_loader()
			},
			success:function(resp){
				if(typeof resp == 'object' && resp.status == 'success'){
					alert_toast("Inquiry sent",'success')
					$('#contactForm').get(0).reset()
				}else{
					console.log(resp)
					alert_toast("an error occured",'error')
					end_loader()
				}
			}
		})
	})
})
</script>