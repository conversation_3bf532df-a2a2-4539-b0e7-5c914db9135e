 <!-- Modern SAAS Navigation -->
 <nav class="navbar navbar-expand-lg fixed-top navbar-modern transition-normal" id="mainNav">
            <div class="container-fluid px-4 lg:px-8">
                <!-- Modern Brand -->
                <a class="navbar-brand-modern transition-normal" href="#page-top">
                    <div class="flex-modern items-center gap-3">
                        <div class="w-10 h-10 bg-gradient-hero rounded-xl flex-modern items-center justify-center shadow-md">
                            <i class="fas fa-mountain text-white text-lg"></i>
                        </div>
                        <span class="text-h4 font-bold">TourismPro</span>
                    </div>
                </a>

                <!-- Mobile Menu Button -->
                <button class="btn-modern btn-modern-ghost lg:hidden" type="button" data-toggle="collapse" data-target="#navbarResponsive" aria-controls="navbarResponsive" aria-expanded="false" aria-label="Toggle navigation">
                    <i class="fas fa-bars text-lg"></i>
                </button>

                <!-- Navigation Links -->
                <div class="collapse navbar-collapse" id="navbarResponsive">
                    <div class="flex-modern items-center gap-2 ms-auto">
                        <a class="nav-link-modern" href="<?php echo $page !='home' ? './':''  ?>">
                            <i class="fas fa-home mr-2"></i>Home
                        </a>
                        <a class="nav-link-modern" href="./?page=packages">
                            <i class="fas fa-suitcase mr-2"></i>Packages
                        </a>
                        <a class="nav-link-modern" href="<?php echo $page !='home' ? './':''  ?>#about">
                            <i class="fas fa-info-circle mr-2"></i>About
                        </a>
                        <a class="nav-link-modern" href="<?php echo $page !='home' ? './':''  ?>#contact">
                            <i class="fas fa-envelope mr-2"></i>Contact
                        </a>

                        <!-- User Authentication -->
                        <?php if(isset($_SESSION['userdata'])): ?>
                            <div class="flex-modern items-center gap-2 ml-4 pl-4 border-l border-secondary-200">
                                <div class="dropdown">
                                    <button class="btn-modern btn-modern-secondary dropdown-toggle" type="button" id="userDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <div class="flex-modern items-center gap-2">
                                            <div class="w-8 h-8 bg-gradient-primary rounded-full flex-modern items-center justify-center">
                                                <i class="fas fa-user text-white text-sm"></i>
                                            </div>
                                            <span class="hidden md:inline"><?php echo $_settings->userdata('firstname') ?></span>
                                        </div>
                                    </button>
                                    <div class="dropdown-menu dropdown-menu-right shadow-xl border-0 rounded-xl mt-2" aria-labelledby="userDropdown">
                                        <a class="dropdown-item flex-modern items-center gap-2 p-3" href="./?page=my_account">
                                            <i class="fas fa-user-circle text-primary-500"></i>
                                            My Account
                                        </a>
                                        <div class="dropdown-divider"></div>
                                        <a class="dropdown-item flex-modern items-center gap-2 p-3 text-danger-600" href="logout.php">
                                            <i class="fas fa-sign-out-alt"></i>
                                            Logout
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="flex-modern items-center gap-3 ml-4 pl-4 border-l border-secondary-200">
                                <button class="btn-modern btn-modern-outline btn-modern-sm" id="login_btn">
                                    <i class="fas fa-sign-in-alt mr-2"></i>Login
                                </button>
                                <button class="btn-modern btn-modern-primary btn-modern-sm">
                                    <i class="fas fa-user-plus mr-2"></i>Sign Up
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </nav>
<script>
  $(function(){
    // Modern Login Modal
    $('#login_btn').click(function(){
      uni_modal("Login to Your Account","login.php","large")
    })

    // Modern Navigation Scroll Effect
    $(window).scroll(function() {
      const scrollTop = $(window).scrollTop();
      const navbar = $('#mainNav');

      if (scrollTop > 50) {
        navbar.addClass('scrolled');
      } else {
        navbar.removeClass('scrolled');
      }
    });

    // Mobile Navigation Toggle
    $('#navbarResponsive').on('show.bs.collapse', function () {
        $('#mainNav').addClass('scrolled')
    })

    $('#navbarResponsive').on('hidden.bs.collapse', function () {
        if($(window).scrollTop() <= 50) {
          $('#mainNav').removeClass('scrolled')
        }
    })

    // Smooth Scroll for Anchor Links
    $('a[href^="#"]').on('click', function(event) {
      const target = $(this.getAttribute('href'));
      if( target.length ) {
        event.preventDefault();
        $('html, body').stop().animate({
          scrollTop: target.offset().top - 80
        }, 800, 'easeInOutExpo');
      }
    });

    // Add active class to current page nav link
    const currentPage = window.location.search;
    $('.nav-link-modern').each(function() {
      const href = $(this).attr('href');
      if (href && (currentPage.includes(href) || (currentPage === '' && href === './'))) {
        $(this).addClass('active');
      }
    });

    // Modern dropdown animations
    $('.dropdown').on('show.bs.dropdown', function () {
      $(this).find('.dropdown-menu').addClass('animate-fade-in-down');
    });

    $('.dropdown').on('hide.bs.dropdown', function () {
      $(this).find('.dropdown-menu').removeClass('animate-fade-in-down');
    });
  })
</script>

<style>
/* Additional Navigation Styles */
.navbar-modern {
  padding: 1rem 0;
  transition: all 0.3s ease;
}

.navbar-modern.scrolled {
  padding: 0.5rem 0;
  background: rgba(255, 255, 255, 0.98) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.dropdown-menu {
  border: none;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  padding: 0.5rem;
  margin-top: 0.5rem;
}

.dropdown-item {
  border-radius: 8px;
  transition: all 0.2s ease;
  font-weight: 500;
}

.dropdown-item:hover {
  background: var(--primary-50);
  color: var(--primary-700);
  transform: translateX(4px);
}

.dropdown-divider {
  margin: 0.5rem 0;
  border-color: var(--secondary-200);
}

/* Mobile Navigation Styles */
@media (max-width: 991px) {
  .navbar-collapse {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    margin-top: 1rem;
    padding: 1.5rem;
  }

  .nav-link-modern {
    padding: 0.75rem 1rem;
    margin: 0.25rem 0;
    border-radius: 8px;
    display: block;
  }

  .nav-link-modern:hover {
    background: var(--primary-50);
    transform: translateX(4px);
  }
}

/* Brand Animation */
.navbar-brand-modern:hover {
  transform: scale(1.05);
}

.navbar-brand-modern .w-10 {
  width: 2.5rem;
  height: 2.5rem;
}

/* Utility Classes for Navigation */
.w-8 { width: 2rem; }
.h-8 { height: 2rem; }
.w-10 { width: 2.5rem; }
.h-10 { height: 2.5rem; }
.border-l { border-left: 1px solid; }
.border-secondary-200 { border-color: var(--secondary-200); }
</style>