 <!-- Clean Navigation -->
 <nav class="navbar navbar-expand-lg navbar-dark fixed-top" id="mainNav">
            <div class="container">
                <!-- Brand -->
                <a class="navbar-brand fw-bold" href="#page-top">
                    <i class="fas fa-mountain me-2"></i>
                    TourismPro
                </a>

                <!-- Mobile Menu Button -->
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarResponsive" aria-controls="navbarResponsive" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- Navigation Links -->
                <div class="collapse navbar-collapse" id="navbarResponsive">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $page !='home' ? './':''  ?>">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="./?page=packages">Packages</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $page !='home' ? './':''  ?>#about">About</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $page !='home' ? './':''  ?>#contact">Contact</a>
                        </li>

                        <!-- User Authentication -->
                        <?php if(isset($_SESSION['userdata'])): ?>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-user me-1"></i>
                                    <?php echo $_settings->userdata('firstname') ?>
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="userDropdown">
                                    <li><a class="dropdown-item" href="./?page=my_account">
                                        <i class="fas fa-user-circle me-2"></i>My Account
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="logout.php">
                                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                                    </a></li>
                                </ul>
                            </li>
                        <?php else: ?>
                            <li class="nav-item">
                                <a class="nav-link" href="javascript:void(0)" id="login_btn">
                                    <i class="fas fa-sign-in-alt me-1"></i>Login
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </nav>
<script>
  $(function(){
    // Login Modal
    $('#login_btn').click(function(){
      uni_modal("Login to Your Account","login.php","large")
    })

    // Navigation Scroll Effect
    $(window).scroll(function() {
      const scrollTop = $(window).scrollTop();
      const navbar = $('#mainNav');

      if (scrollTop > 50) {
        navbar.addClass('navbar-shrink');
      } else {
        navbar.removeClass('navbar-shrink');
      }
    });

    // Smooth Scroll for Anchor Links
    $('a[href^="#"]').on('click', function(event) {
      const target = $(this.getAttribute('href'));
      if( target.length ) {
        event.preventDefault();
        $('html, body').stop().animate({
          scrollTop: target.offset().top - 80
        }, 800);
      }
    });
  })
</script>

<style>
/* Clean Navigation Styles */
#mainNav {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  padding: 1rem 0;
}

#mainNav.navbar-shrink {
  padding: 0.5rem 0;
  background: rgba(0, 0, 0, 0.95);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
}

.navbar-brand {
  font-size: 1.5rem;
  color: #ffc800 !important;
  transition: all 0.3s ease;
}

.navbar-brand:hover {
  transform: scale(1.05);
  color: #fff !important;
}

.nav-link {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem !important;
  border-radius: 0.5rem;
}

.nav-link:hover {
  color: #ffc800 !important;
  background: rgba(255, 200, 0, 0.1);
  transform: translateY(-1px);
}

.dropdown-menu {
  background: rgba(0, 0, 0, 0.9);
  border: none;
  border-radius: 0.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.dropdown-item {
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
}

.dropdown-item:hover {
  background: rgba(255, 200, 0, 0.1);
  color: #ffc800;
}
</style>